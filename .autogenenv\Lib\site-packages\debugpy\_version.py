
# This file was generated by 'versioneer.py' (0.23) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2025-09-05T09:14:53-0700",
 "dirty": false,
 "error": null,
 "full-revisionid": "6cbdf8767e4c88dfaedf3db7b09ce2781496fc51",
 "version": "1.8.17"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
