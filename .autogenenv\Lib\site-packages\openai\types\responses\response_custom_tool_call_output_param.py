# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing_extensions import Literal, Required, TypedDict

__all__ = ["ResponseCustomToolCallOutputParam"]


class ResponseCustomToolCallOutputParam(TypedDict, total=False):
    call_id: Required[str]
    """The call ID, used to map this custom tool call output to a custom tool call."""

    output: Required[str]
    """The output from the custom tool call generated by your code."""

    type: Required[Literal["custom_tool_call_output"]]
    """The type of the custom tool call output. Always `custom_tool_call_output`."""

    id: str
    """The unique ID of the custom tool call output in the OpenAI platform."""
