"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import abc
from . import agent_worker_pb2
import collections.abc
import grpc
import grpc.aio
import typing

_T = typing.TypeVar("_T")

class _MaybeAsyncIterator(collections.abc.AsyncIterator[_T], collections.abc.Iterator[_T], metaclass=abc.ABCMeta): ...

class _ServicerContext(grpc.ServicerContext, grpc.aio.ServicerContext):  # type: ignore[misc, type-arg]
    ...

class AgentRpcStub:
    def __init__(self, channel: typing.Union[grpc.Channel, grpc.aio.Channel]) -> None: ...
    OpenChannel: grpc.StreamStreamMultiCallable[
        agent_worker_pb2.Message,
        agent_worker_pb2.Message,
    ]

    OpenControlChannel: grpc.StreamStreamMultiCallable[
        agent_worker_pb2.ControlMessage,
        agent_worker_pb2.ControlMessage,
    ]

    RegisterAgent: grpc.UnaryUnaryMultiCallable[
        agent_worker_pb2.RegisterAgentTypeRequest,
        agent_worker_pb2.RegisterAgentTypeResponse,
    ]

    AddSubscription: grpc.UnaryUnaryMultiCallable[
        agent_worker_pb2.AddSubscriptionRequest,
        agent_worker_pb2.AddSubscriptionResponse,
    ]

    RemoveSubscription: grpc.UnaryUnaryMultiCallable[
        agent_worker_pb2.RemoveSubscriptionRequest,
        agent_worker_pb2.RemoveSubscriptionResponse,
    ]

    GetSubscriptions: grpc.UnaryUnaryMultiCallable[
        agent_worker_pb2.GetSubscriptionsRequest,
        agent_worker_pb2.GetSubscriptionsResponse,
    ]

class AgentRpcAsyncStub:
    OpenChannel: grpc.aio.StreamStreamMultiCallable[
        agent_worker_pb2.Message,
        agent_worker_pb2.Message,
    ]

    OpenControlChannel: grpc.aio.StreamStreamMultiCallable[
        agent_worker_pb2.ControlMessage,
        agent_worker_pb2.ControlMessage,
    ]

    RegisterAgent: grpc.aio.UnaryUnaryMultiCallable[
        agent_worker_pb2.RegisterAgentTypeRequest,
        agent_worker_pb2.RegisterAgentTypeResponse,
    ]

    AddSubscription: grpc.aio.UnaryUnaryMultiCallable[
        agent_worker_pb2.AddSubscriptionRequest,
        agent_worker_pb2.AddSubscriptionResponse,
    ]

    RemoveSubscription: grpc.aio.UnaryUnaryMultiCallable[
        agent_worker_pb2.RemoveSubscriptionRequest,
        agent_worker_pb2.RemoveSubscriptionResponse,
    ]

    GetSubscriptions: grpc.aio.UnaryUnaryMultiCallable[
        agent_worker_pb2.GetSubscriptionsRequest,
        agent_worker_pb2.GetSubscriptionsResponse,
    ]

class AgentRpcServicer(metaclass=abc.ABCMeta):
    @abc.abstractmethod
    def OpenChannel(
        self,
        request_iterator: _MaybeAsyncIterator[agent_worker_pb2.Message],
        context: _ServicerContext,
    ) -> typing.Union[collections.abc.Iterator[agent_worker_pb2.Message], collections.abc.AsyncIterator[agent_worker_pb2.Message]]: ...

    @abc.abstractmethod
    def OpenControlChannel(
        self,
        request_iterator: _MaybeAsyncIterator[agent_worker_pb2.ControlMessage],
        context: _ServicerContext,
    ) -> typing.Union[collections.abc.Iterator[agent_worker_pb2.ControlMessage], collections.abc.AsyncIterator[agent_worker_pb2.ControlMessage]]: ...

    @abc.abstractmethod
    def RegisterAgent(
        self,
        request: agent_worker_pb2.RegisterAgentTypeRequest,
        context: _ServicerContext,
    ) -> typing.Union[agent_worker_pb2.RegisterAgentTypeResponse, collections.abc.Awaitable[agent_worker_pb2.RegisterAgentTypeResponse]]: ...

    @abc.abstractmethod
    def AddSubscription(
        self,
        request: agent_worker_pb2.AddSubscriptionRequest,
        context: _ServicerContext,
    ) -> typing.Union[agent_worker_pb2.AddSubscriptionResponse, collections.abc.Awaitable[agent_worker_pb2.AddSubscriptionResponse]]: ...

    @abc.abstractmethod
    def RemoveSubscription(
        self,
        request: agent_worker_pb2.RemoveSubscriptionRequest,
        context: _ServicerContext,
    ) -> typing.Union[agent_worker_pb2.RemoveSubscriptionResponse, collections.abc.Awaitable[agent_worker_pb2.RemoveSubscriptionResponse]]: ...

    @abc.abstractmethod
    def GetSubscriptions(
        self,
        request: agent_worker_pb2.GetSubscriptionsRequest,
        context: _ServicerContext,
    ) -> typing.Union[agent_worker_pb2.GetSubscriptionsResponse, collections.abc.Awaitable[agent_worker_pb2.GetSubscriptionsResponse]]: ...

def add_AgentRpcServicer_to_server(servicer: AgentRpcServicer, server: typing.Union[grpc.Server, grpc.aio.Server]) -> None: ...
