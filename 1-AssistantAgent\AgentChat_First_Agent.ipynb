from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient
from dotenv import load_dotenv
import os

# Load environment variables from .env file
load_dotenv()

# Get Gemini API key from environment
gemini_api_key = os.getenv('GEMINI_API_KEY')

# Create Gemini client using OpenAI-compatible interface
# Gemini models work with the OpenAI client when using the correct base URL
gemini_client = OpenAIChatCompletionClient(
    model="gemini-2.0-flash",
    api_key=gemini_api_key
)

# Create AssistantAgent with Gemini client
assistant = AssistantAgent(
    name="assistant",
    model_client=gemini_client,
    system_message="You are a helpful AI assistant powered by Google's Gemini."
)

print("Gemini-powered AssistantAgent is ready!")
print(f"Using model: {gemini_client.model}")
